from app.enums.workflow_types import WorkflowType
from app.services.dify_service import DifyService
from app.services.sop_service import SOPService


def get_dify_service_by_type(workflow_type: str) -> DifyService:
    """根据工作流类型获取DifyService实例"""
    return DifyService(workflow_type)


def get_sop_service_by_file_type(file_type: int) -> SOPService:
    """根据file_type获取对应的SOP服务实例"""
    workflow_type = WorkflowType.get_sop_workflow_by_file_type(file_type)
    dify_service = DifyService(workflow_type)
    return SOPService(dify_service)
